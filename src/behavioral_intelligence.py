"""
<PERSON><PERSON>ger ORB Timeframes - Opening Range Breakout focused timeframe generation.

ORB-FOCUSED ARCHITECTURE:
- backtesting.py: Handles OHLC resampling (superior, battle-tested)
- Our code: Adds ONLY Opening Range Breakout context
- Zero complexity: Focus exclusively on ORB patterns
"""

import pandas as pd
import numpy as np
from typing import Dict
from backtesting.lib import OHLCV_AGG


def calculate_session_candle_numbers(data: pd.DataFrame) -> pd.Series:
    """
    Calculate session candle numbers that reset at the start of each trading session.

    CRITICAL FIX: Session candle numbers should reset at session start (e.g., 8:00 AM for London),
    not at calendar day start (midnight). This ensures candles_since_session_start condition works correctly.

    Args:
        data: DataFrame with DatetimeIndex

    Returns:
        Series with session candle numbers (1, 2, 3, ... resetting at each session start)
    """
    if not isinstance(data.index, pd.DatetimeIndex):
        # Fallback: use daily reset if no datetime index
        return data.groupby(data.index.date if hasattr(data.index, 'date') else data.index).cumcount() + 1

    # Define session start hours for different sessions
    session_starts = {
        'london': 8,    # 8:00 AM GMT
        'ny': 13,       # 1:00 PM GMT (8:00 AM EST)
        'asian': 22     # 10:00 PM GMT (7:00 AM JST)
    }

    # For now, use London session as primary (most common in ORB patterns)
    london_start_hour = session_starts['london']

    # Create session groups: each session starts at london_start_hour
    session_groups = []
    current_session = 0

    for i, timestamp in enumerate(data.index):
        # Check if this is a new session start
        if i == 0:
            # First bar is always session 0
            session_groups.append(current_session)
        else:
            prev_timestamp = data.index[i-1]

            # New session if:
            # 1. Different day AND current hour >= session start hour
            # 2. Same day but crossed session start hour
            if (timestamp.date() != prev_timestamp.date() and timestamp.hour >= london_start_hour) or \
               (timestamp.date() == prev_timestamp.date() and
                prev_timestamp.hour < london_start_hour and timestamp.hour >= london_start_hour):
                current_session += 1

            session_groups.append(current_session)

    # Convert to pandas Series and calculate cumulative count within each session
    session_series = pd.Series(session_groups, index=data.index)
    session_candle_numbers = session_series.groupby(session_series).cumcount() + 1

    return session_candle_numbers


def generate_orb_timeframes(data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """
    ORB-FOCUSED timeframe generation: backtesting.py resampling + Opening Range Breakout context ONLY.

    ARCHITECTURE:
    1. Use backtesting.py's superior OHLCV_AGG for resampling (no manual code)
    2. Add ONLY Opening Range Breakout context - NO OTHER METRICS
    3. Focus exclusively on intraday ORB patterns

    Args:
        data: M1 OHLCV DataFrame with datetime index

    Returns:
        Dictionary of timeframe DataFrames with ORB context ONLY
    """
    print("📊 ORB-FOCUSED timeframe generation: backtesting.py + Opening Range Breakout context...")

    timeframes = {}

    # ORB-FOCUSED timeframe configurations - INTRADAY ONLY
    timeframe_configs = {
        '1min': '1min',    # 1-minute bars for precise ORB
        '5min': '5min',    # 5-minute bars
        '10min': '10min',  # 10-minute bars (NEW for ORB)
        '15min': '15min',  # 15-minute bars
        '30min': '30min',  # 30-minute bars
        '60min': '60min'   # 60-minute bars (1-hour)
    }

    # ARCHITECTURAL FIX: Ensure data has required columns (proper capitalization)
    # Data comes from backtesting.py with proper capitalization: Open, High, Low, Close, Volume
    required_cols = ['Open', 'High', 'Low', 'Close']
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        raise ValueError(f"Data missing required columns: {missing_cols}")

    # Add Volume if missing
    if 'Volume' not in data.columns:
        data = data.copy()
        data['Volume'] = np.nan

    print(f"   📊 Input data: {len(data)} M1 bars from {data.index[0]} to {data.index[-1]}")

    # Generate each timeframe using ORB-FOCUSED architecture
    for tf_name, freq in timeframe_configs.items():
        try:
            print(f"   🔄 {tf_name}: backtesting.py resampling + ORB context...")

            # CLEAN: Use backtesting.py's superior OHLCV aggregation (zero manual code)
            clean_resampled = data.resample(freq, label='right').agg(OHLCV_AGG).dropna()

            if clean_resampled.empty:
                print(f"   ⚠️  {tf_name} resulted in empty data - skipping")
                continue

            # ORB-FOCUSED: Add Opening Range Breakout context
            orb_data = add_orb_context(clean_resampled, tf_name)

            timeframes[tf_name] = orb_data
            print(f"   ✅ {tf_name}: {len(orb_data)} bars with ORB context")

        except Exception as e:
            print(f"   ❌ Failed to generate {tf_name}: {e}")
            continue

    print(f"✅ ORB timeframe generation complete: {len(timeframes)} timeframes")
    print("   🚀 backtesting.py: Superior OHLC resampling")
    print("   🎯 Our code: Opening Range Breakout context")
    return timeframes


def add_orb_context(data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    Add ONLY Opening Range Breakout context to clean backtesting.py resampled data.

    ORB-FOCUSED ARCHITECTURE:
    - Input: Clean OHLC data from backtesting.py's superior resampling
    - Output: Data with Opening Range Breakout context
    - Zero complexity: ORB-relevant metrics

    Args:
        data: Clean OHLC DataFrame from backtesting.py
        timeframe: Timeframe identifier (e.g., '5min', '15min')

    Returns:
        DataFrame with ORB context ONLY
    """
    orb_data = data.copy()

    print(f"   🎯 Adding ORB context for {timeframe}")

    try:
        # Basic time context for ORB
        orb_data['hour'] = orb_data.index.hour
        orb_data['minute'] = orb_data.index.minute
        orb_data['timeframe'] = timeframe

        # Basic directional context (needed for ORB)
        orb_data['bullish_bar'] = orb_data['Close'] > orb_data['Open']
        orb_data['bearish_bar'] = orb_data['Close'] < orb_data['Open']

        # ORB-specific metrics
        orb_data['bar_range'] = orb_data['High'] - orb_data['Low']
        orb_data['is_opening_session'] = (orb_data['hour'] >= 8) & (orb_data['hour'] <= 10)  # London/NY opening hours

        # Simple breakout detection (price above/below previous high/low)
        orb_data['above_prev_high'] = orb_data['Close'] > orb_data['High'].shift(1)
        orb_data['below_prev_low'] = orb_data['Close'] < orb_data['Low'].shift(1)

        # ORB-specific opening range calculation (flexible candle count)
        # Opening range can be based on 1st, 2nd, 3rd, or more candles - whatever works
        orb_data['date'] = orb_data.index.date

        # Calculate session candle numbers for candles_since_session_start condition
        # CRITICAL FIX: Reset candle numbers at start of each trading session, not calendar day
        orb_data['session_candle_number'] = calculate_session_candle_numbers(orb_data)

        # SESSION-AWARE opening range calculation (CRITICAL FIX)
        # Calculate ORB based on TRADING SESSIONS, not calendar days
        session_opening_ranges = {}

        # Define session start hours in UTC+1 (data timezone)
        session_hours = {
            'london': 9,    # London opens at 9:00 AM UTC+1
            'ny': 15,       # NY opens at 15:30 UTC+1 (rounded to 15)
            'asian': 1      # Asian opens at 1:00 AM UTC+1
        }

        # Group data by date for session processing
        for date in orb_data['date'].unique():
            day_data = orb_data[orb_data['date'] == date].copy()
            if len(day_data) == 0:
                continue

            # Find session starts within this day
            for session_name, start_hour in session_hours.items():
                # Find bars that start this session (within 2 hours of session start)
                session_mask = (day_data['hour'] >= start_hour) & (day_data['hour'] < start_hour + 2)
                session_data = day_data[session_mask]

                if len(session_data) >= 2:  # Need at least 2 bars for ORB
                    # Use first 2 bars of session for opening range
                    opening_data = session_data.head(2)
                    session_key = f"{date}_{session_name}"
                    session_opening_ranges[session_key] = {
                        'high': opening_data['High'].max(),
                        'low': opening_data['Low'].min(),
                        'session': session_name,
                        'start_hour': start_hour
                    }

        # Map ORB levels to data based on current session context
        def get_orb_for_bar(row):
            bar_date = row['date']
            bar_hour = row['hour']

            # Determine which session this bar belongs to
            current_session = None
            if 9 <= bar_hour < 18:
                current_session = 'london'
            elif 15 <= bar_hour < 22:
                current_session = 'ny'  # NY can overlap with London
            elif 1 <= bar_hour <= 7:
                current_session = 'asian'

            if current_session:
                session_key = f"{bar_date}_{current_session}"
                return session_opening_ranges.get(session_key, {})
            return {}

        # Apply ORB levels to each bar
        orb_levels = orb_data.apply(get_orb_for_bar, axis=1)
        orb_data['opening_range_high'] = orb_levels.apply(lambda x: x.get('high', np.nan))
        orb_data['opening_range_low'] = orb_levels.apply(lambda x: x.get('low', np.nan))

        # ORB breakout signals
        orb_data['orb_breakout_up'] = orb_data['Close'] > orb_data['opening_range_high']
        orb_data['orb_breakout_down'] = orb_data['Close'] < orb_data['opening_range_low']
        orb_data['orb_breakout'] = orb_data['orb_breakout_up'] | orb_data['orb_breakout_down']

        # Clean up temporary date column
        orb_data = orb_data.drop('date', axis=1)

        # Clean up any infinite or NaN values
        orb_data = orb_data.replace([np.inf, -np.inf], np.nan)
        orb_data = orb_data.ffill().fillna(0)

        print(f"   ✅ ORB context added: {len(orb_data)} bars with opening range breakout signals")
        return orb_data

    except Exception as e:
        print(f"   ⚠️  Error adding ORB context to {timeframe}: {e}")
        # Return basic data if ORB enhancement fails
        orb_data['hour'] = orb_data.index.hour if isinstance(orb_data.index, pd.DatetimeIndex) else 0
        orb_data['timeframe'] = timeframe
        orb_data['bullish_bar'] = orb_data['Close'] > orb_data['Open']
        orb_data['bearish_bar'] = orb_data['Close'] < orb_data['Open']
        orb_data['bar_range'] = orb_data['High'] - orb_data['Low']
        orb_data['is_opening_session'] = False
        orb_data['above_prev_high'] = False
        orb_data['below_prev_low'] = False
        orb_data['opening_range_high'] = np.nan
        orb_data['opening_range_low'] = np.nan
        orb_data['orb_breakout_up'] = False
        orb_data['orb_breakout_down'] = False
        orb_data['orb_breakout'] = False
        orb_data['session_candle_number'] = 1  # Default to first candle
        return orb_data


def _get_bars_per_day(timeframe: str) -> float:
    """
    Helper function to calculate expected bars per day for a given timeframe.

    Args:
        timeframe: Timeframe string (e.g., '5min', '1H')

    Returns:
        Number of bars expected per 24-hour day
    """
    # Full 24-hour day (1440 minutes)
    timeframe_minutes = {
        '1min': 1, '5min': 5, '10min': 10, '15min': 15, '30min': 30,
        '60min': 60, '1h': 60, '1H': 60, '2H': 120, '4h': 240, '4H': 240,
        '1d': 1440, '1D': 1440, '1w': 1440 * 7, '1W': 1440 * 7
    }

    minutes = timeframe_minutes.get(timeframe, 60)  # Default to 1 hour
    minutes_per_day = 1440  # 24 hours

    return minutes_per_day / minutes


def add_behavioral_intelligence(data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    Add behavioral intelligence features to OHLC data.

    This function provides backward compatibility with existing tests while
    leveraging the ORB-focused architecture. It adds basic behavioral features
    and ORB context to the data.

    Args:
        data: DataFrame with OHLC data
        timeframe: Timeframe string (e.g., '5min', '1H')

    Returns:
        DataFrame with added behavioral intelligence features
    """
    try:
        # Start with ORB context
        result = add_orb_context(data.copy(), timeframe)

        # Add basic behavioral features expected by tests
        if not result.empty:
            # Time-based features
            if isinstance(result.index, pd.DatetimeIndex):
                result['hour'] = result.index.hour
                result['minute'] = result.index.minute
                result['day_of_week_num'] = result.index.dayofweek
            else:
                result['hour'] = 0
                result['minute'] = 0
                result['day_of_week_num'] = 0

            # Timeframe classification
            result['timeframe'] = timeframe
            intraday_timeframes = ['1min', '5min', '10min', '15min', '30min', '60min']
            result['intraday'] = timeframe in intraday_timeframes
            result['daily_plus'] = timeframe not in intraday_timeframes

            # Basic price action features
            if all(col in result.columns for col in ['Open', 'High', 'Low', 'Close']):
                result['body_size'] = abs(result['Close'] - result['Open'])
                result['wick_upper'] = result['High'] - result[['Open', 'Close']].max(axis=1)
                result['wick_lower'] = result[['Open', 'Close']].min(axis=1) - result['Low']
                result['range_size'] = result['High'] - result['Low']
                result['price_change'] = result['Close'] - result['Open']

                # Bullish/bearish indicators
                result['bullish'] = result['Close'] > result['Open']
                result['bearish'] = result['Close'] < result['Open']
                result['doji'] = abs(result['Close'] - result['Open']) < (result['range_size'] * 0.1)

                # Momentum features (simple rolling calculations)
                if len(result) >= 3:
                    result['momentum_3'] = result['Close'].rolling(3).mean().fillna(0)
                else:
                    result['momentum_3'] = result['Close']

                if len(result) >= 5:
                    result['momentum_5'] = result['Close'].rolling(5).mean().fillna(0)
                else:
                    result['momentum_5'] = result['Close']

                # Simple pattern recognition
                result['hammer'] = (result['wick_lower'] > result['body_size'] * 2) & result['bullish']
                result['shooting_star'] = (result['wick_upper'] > result['body_size'] * 2) & result['bearish']
                result['engulfing_bull'] = result['bullish'] & (result['body_size'] > result['body_size'].shift(1))
                result['engulfing_bear'] = result['bearish'] & (result['body_size'] > result['body_size'].shift(1))

            # Volatility features
            if 'range_size' in result.columns:
                volatility_mean = result['range_size'].mean()
                result['volatility'] = result['range_size']
                result['high_volatility'] = result['range_size'] > volatility_mean * 1.5
                result['volatility_regime'] = 'high'  # Simplified regime classification
                result['volatility_regime'] = result['volatility_regime'].where(
                    result['high_volatility'], 'normal'
                )
                # Add moving average for volatility
                if len(result) >= 5:
                    result['volatility_ma'] = result['volatility'].rolling(5).mean().fillna(result['volatility'])
                else:
                    result['volatility_ma'] = result['volatility']

                # Add trend regime based on price movement
                if 'Close' in result.columns and len(result) >= 3:
                    price_trend = result['Close'].rolling(3).mean()
                    result['trend_regime'] = 'up'
                    result['trend_regime'] = result['trend_regime'].where(
                        price_trend > price_trend.shift(1), 'down'
                    ).fillna('sideways')
                else:
                    result['trend_regime'] = 'sideways'
            else:
                result['volatility'] = 0
                result['high_volatility'] = False
                result['volatility_regime'] = 'normal'
                result['volatility_ma'] = 0
                result['trend_regime'] = 'sideways'

            # Volume features
            if 'Volume' in result.columns:
                volume_mean = result['Volume'].mean()
                result['high_volume'] = result['Volume'] > volume_mean * 1.5
                result['volume_breakout'] = result['Volume'] > volume_mean * 2.0
            else:
                # Use price volatility as volume proxy
                if 'high_volatility' in result.columns:
                    result['high_volume'] = result['high_volatility']
                    result['volume_breakout'] = result['high_volatility']
                else:
                    result['high_volume'] = False
                    result['volume_breakout'] = False

            # Handle day_of_week string mapping if present
            if 'day_of_week' in result.columns:
                day_mapping = {
                    'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,
                    'Friday': 4, 'Saturday': 5, 'Sunday': 6
                }
                result['day_of_week_num'] = result['day_of_week'].map(day_mapping).fillna(0).astype(int)

            # Clean up infinite and NaN values
            numeric_cols = result.select_dtypes(include=[np.number]).columns
            result[numeric_cols] = result[numeric_cols].replace([np.inf, -np.inf], np.nan).fillna(0)

        return result

    except Exception as e:
        # Fallback: return basic structure with minimal features
        result = data.copy()
        result['hour'] = 0
        result['timeframe'] = timeframe
        result['bullish'] = False
        result['bearish'] = False
        result['day_of_week_num'] = 0
        return result


def generate_orb_summaries(timeframe_data: Dict[str, pd.DataFrame]) -> str:
    """
    Generate CONCISE ORB summaries for LLM pattern discovery.
    Focused on Opening Range Breakout patterns - NO behavioral metrics.
    """
    summaries = []

    for tf_name, df in timeframe_data.items():
        if df.empty:
            continue

        try:
            # ORB-specific metrics
            total_bars = len(df)
            bullish_bars = df['bullish_bar'].sum() if 'bullish_bar' in df.columns else 0
            orb_breakouts = df['orb_breakout'].sum() if 'orb_breakout' in df.columns else 0
            orb_up_breakouts = df['orb_breakout_up'].sum() if 'orb_breakout_up' in df.columns else 0
            orb_down_breakouts = df['orb_breakout_down'].sum() if 'orb_breakout_down' in df.columns else 0

            # Calculate percentages
            bullish_pct = (bullish_bars / total_bars * 100) if total_bars > 0 else 0
            orb_breakout_pct = (orb_breakouts / total_bars * 100) if total_bars > 0 else 0

            # Most active trading hours
            most_active_hour = df.groupby('hour').size().idxmax() if len(df) > 0 and 'hour' in df.columns else 0

            # ORB-focused summary
            summary = f"{tf_name.upper()}: {total_bars} bars | Bullish: {bullish_pct:.0f}% | ORB Breakouts: {orb_breakouts} ({orb_breakout_pct:.1f}%) | Up: {orb_up_breakouts} | Down: {orb_down_breakouts} | Peak Hour: {most_active_hour}"

            summaries.append(summary)

        except Exception as e:
            print(f"   ⚠️  Error generating ORB summary for {tf_name}: {e}")
            summaries.append(f"{tf_name.upper()}: ORB Error - {e}")

    return " | ".join(summaries)


# ORB-Focused Usage:
"""
# Load M1 data
data = pd.read_csv('your_m1_data.csv', index_col=0, parse_dates=True)

# Generate ORB-focused timeframes (1m, 5m, 10m, 15m, 30m, 60m)
timeframes = generate_orb_timeframes(data)

# Generate ORB summaries (NO behavioral metrics)
summaries = generate_orb_summaries(timeframes)
print(summaries)
"""


# Backward compatibility aliases for tests
# These provide compatibility while maintaining ORB-focused architecture
def generate_clean_timeframes(data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """Backward compatibility alias for generate_orb_timeframes."""
    return generate_orb_timeframes(data)


def generate_behavioral_summaries(timeframe_data: Dict[str, pd.DataFrame]) -> str:
    """Backward compatibility alias for generate_orb_summaries."""
    return generate_orb_summaries(timeframe_data)
