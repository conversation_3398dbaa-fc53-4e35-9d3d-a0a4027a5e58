#!/usr/bin/env python3
"""
🎯 FILE GENERATOR MODULE

Handles ALL file generation for the Jaeger trading system:
- Trading system .md reports (using backtesting.py statistics)
- MT4 EA .mq4 files
- HTML interactive charts (using backtesting.py charts)
- Result folder organization

CRITICAL: backtesting.py is the SOURCE OF TRUTH for:
- All trade statistics
- All interactive HTML charts
- All performance metrics
"""

import os
from datetime import datetime
from config import config
import logging
from logging_utils import log_error, log_warning, log_info
from fact_checker import LLMFactChecker

logger = logging.getLogger(__name__)

class FileGenerator:
    """Handles all file generation for trading systems"""
    
    def __init__(self):
        """Initialize file generator"""
        self.results_dir = config.RESULTS_DIR
        os.makedirs(self.results_dir, exist_ok=True)
    
    def generate_trading_system_files(self, cortex_results, backtest_results=None, validation_results=None):
        """
        Generate complete trading system files from new backtesting-only architecture

        Args:
            cortex_results: Results from Cortex LLM coordination
            backtest_results: Results from backtesting module (with backtesting.py stats)
            validation_results: Results from walk-forward validation (contains profitable patterns)

        Returns:
            dict: Paths to all generated files
        """
        log_info("📁 GENERATING TRADING SYSTEM FILES...")
        log_info("   📊 Source: backtesting.py statistics and charts")
        log_info("   🧠 Source: Cortex LLM analysis")
        
        # Extract data from Cortex results
        symbol = cortex_results.get('symbol', 'UNKNOWN')
        llm_analysis = cortex_results.get('llm_analysis', '')
        mt4_ea_code = cortex_results.get('mt4_ea_code', '')
        ea_name = cortex_results.get('ea_name', 'Unknown_EA')
        ohlc_data = cortex_results.get('ohlc_data')
        
        # Create timestamped folder for this trading system
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        system_folder = os.path.join(self.results_dir, f"{symbol}_{timestamp}")
        os.makedirs(system_folder, exist_ok=True)
        
        generated_files = {
            'system_folder': system_folder,
            'timestamp': timestamp
        }
        
        # 1. Generate trading system .md report
        md_file = self._generate_trading_system_report(
            system_folder, symbol, llm_analysis, backtest_results, timestamp
        )
        generated_files['trading_system_report'] = md_file
        
        # 2. Generate MT4 EA .mq4 files (individual EAs per pattern)
        ea_files = self._generate_validated_mt4_ea(system_folder, ea_name, validation_results, backtest_results)
        generated_files['mt4_ea_files'] = ea_files  # Now returns list of files
        
        # 3. Generate HTML interactive charts (using backtesting.py)
        html_files = self._generate_html_charts(
            system_folder, symbol, ohlc_data, backtest_results
        )
        generated_files['html_charts'] = html_files
        
        # 4. Generate summary CSV with all trade data
        csv_files = self._generate_trade_csv_files(system_folder, symbol, backtest_results)
        generated_files['csv_files'] = csv_files
        
        print(f"✅ Trading system files generated in: {system_folder}")
        return generated_files

    def _fact_check_llm_analysis(self, llm_analysis, backtest_results):
        """Fact-check LLM analysis against actual backtest data to prevent hallucinations"""
        print("🔍 Fact-checking LLM analysis for .md report...")

        # Create a mock data object for the fact checker
        # Since we don't have the original OHLC data here, we'll create a minimal data structure
        # The fact checker mainly looks for fabricated metrics, not OHLC data
        import pandas as pd

        # Create minimal data structure for fact checker
        mock_data = pd.DataFrame({
            'hour': list(range(24)),  # Basic hour data for time claims
            'volume': [1000] * 24     # Basic volume data for volume claims
        })

        try:
            fact_checker = LLMFactChecker(mock_data)
            fact_checked_analysis = fact_checker.validate_response(llm_analysis)
            print("✅ LLM analysis fact-checked for report generation")
            return fact_checked_analysis
        except Exception as e:
            print(f"⚠️ Fact-checking failed: {e}")
            print("   Using original LLM analysis without fact-checking")
            return llm_analysis

    def _generate_trading_system_report(self, folder, symbol, llm_analysis, backtest_results, timestamp):
        """Generate comprehensive .md trading system report using backtesting.py statistics"""

        report_file = os.path.join(folder, f"{symbol}_trading_system_{timestamp}.md")

        # Fact-check the LLM analysis to prevent hallucinations in the report
        fact_checked_analysis = self._fact_check_llm_analysis(llm_analysis, backtest_results)

        # Build report content
        report = f"""# 🧠 JAEGER TRADING SYSTEM - {symbol}

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**System**: Jaeger AI Pattern Discovery
**Symbol**: {symbol}
**Methodology**: Situational Analysis + LLM Pattern Discovery

## 🎯 TRADING COSTS CONFIGURATION

**Spread**: 1 pip (0.0001) - Realistic trading cost
**Commission**: None
**LLM Awareness**: AI patterns account for 1-pip spread cost

---

## 🧠 LLM PATTERN ANALYSIS

{fact_checked_analysis}

---

## 📊 BACKTESTING RESULTS

**Source**: backtesting.py professional framework
**Data Quality**: Real market data with realistic spread costs

"""
        
        # Add backtesting results if available
        if backtest_results:
            for i, result in enumerate(backtest_results, 1):
                stats = result.get('backtesting_py_stats')
                if stats is not None and not (hasattr(stats, 'empty') and stats.empty):
                    report += f"""
### Pattern {i} Performance

**Source**: backtesting.py comprehensive statistics

"""
                    # Use backtesting.py statistics directly
                    report += self._format_backtesting_py_stats(stats)
                    
                    # Reference to HTML chart
                    report += f"""
📊 **Interactive Chart**: `{symbol}_pattern_{i}_chart.html`
- Candlestick charts with trade markers
- Equity curve analysis  
- Drawdown visualization
- Generated by backtesting.py

"""
                else:
                    report += f"""
### Pattern {i} Performance

❌ No backtesting.py statistics available
⚠️ Pattern may not have generated trades

"""
        else:
            report += """
### Backtesting Status

⚠️ No backtesting results provided
🔄 Run backtesting module to generate performance statistics

"""
        
        report += f"""
---

## 🤖 MT4 EXPERT ADVISOR

**Files**: Individual EAs per profitable pattern
**Naming**: `GipsyDanger_{symbol}_XXX_Pattern_Y.mq4`
**Compatibility**: MT4/MT5 platforms
**Implementation**: Direct LLM pattern translation

---

## 📁 FILE STRUCTURE

```
{symbol}_{timestamp}/
├── {symbol}_trading_system_{timestamp}.md    # This report
├── {symbol}_EA_{timestamp}.mq4               # MT4 Expert Advisor
├── {symbol}_pattern_*_chart.html             # Interactive charts (backtesting.py)
└── {symbol}_trades_*.csv                     # Trade data (backtesting.py)
```

---

*Generated by Jaeger AI Trading System*
*Powered by backtesting.py framework*
"""
        
        # Save report
        try:
            # Backup if file exists
            if os.path.exists(report_file):
                backup_file = report_file + ".bak_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                os.rename(report_file, backup_file)
                print(f"   ⚠️ Existing markdown report backed up to {os.path.basename(backup_file)}")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"   ✅ Trading system report: {os.path.basename(report_file)}")
            return report_file
        except Exception as e:
            print(f"   ❌ Failed to write trading system report: {e}")
            return None
    
    def _format_backtesting_py_stats(self, stats):
        """Format ALL backtesting.py statistics for comprehensive markdown report"""
        
        # Handle invalid stats input
        if not hasattr(stats, 'get') or not callable(getattr(stats, 'get', None)):
            return "**Invalid stats format** - Unable to format statistics\n"

        # Extract key metrics from backtesting.py stats
        total_trades = stats.get('# Trades', 0)
        if total_trades == 0:
            return "**No trades executed** - Pattern conditions not met\n"

        return f"""
**📊 COMPREHENSIVE PERFORMANCE METRICS** (backtesting.py):

**Core Performance:**
- **Total Return**: {stats.get('Return [%]', 0):.2f}%
- **Annualized Return**: {stats.get('Return (Ann.) [%]', 0):.2f}%
- **CAGR**: {stats.get('CAGR [%]', stats.get('Return (Ann.) [%]', 0)):.2f}%
- **Buy & Hold Return**: {stats.get('Buy & Hold Return [%]', 0):.2f}%
- **Volatility (Ann.)**: {stats.get('Volatility (Ann.) [%]', 0):.2f}%

**Risk Metrics:**
- **Max Drawdown**: {stats.get('Max. Drawdown [%]', 0):.2f}%
- **Avg Drawdown**: {stats.get('Avg. Drawdown [%]', 0):.2f}%
- **Max DD Duration**: {stats.get('Max. Drawdown Duration', 'N/A')}
- **Avg DD Duration**: {stats.get('Avg. Drawdown Duration', 'N/A')}
- **Exposure Time**: {stats.get('Exposure Time [%]', 0):.1f}%

**Risk-Adjusted Returns:**
- **Sharpe Ratio**: {stats.get('Sharpe Ratio', 0):.3f}
- **Sortino Ratio**: {stats.get('Sortino Ratio', 0):.3f}
- **Calmar Ratio**: {stats.get('Calmar Ratio', 0):.3f}
- **Alpha**: {stats.get('Alpha [%]', 0):.2f}%
- **Beta**: {stats.get('Beta', 0):.3f}

**Equity Analysis:**
- **Start Equity**: ${stats.get('Start', 0):,.2f}
- **Final Equity**: ${stats.get('Equity Final [$]', 0):,.2f}
- **Peak Equity**: ${stats.get('Equity Peak [$]', 0):,.2f}

**Trade Statistics:**
- **Total Trades**: {total_trades}
- **Win Rate**: {stats.get('Win Rate [%]', 0):.1f}%
- **Best Trade**: {stats.get('Best Trade [%]', 0):.2f}%
- **Worst Trade**: {stats.get('Worst Trade [%]', 0):.2f}%
- **Average Trade**: {stats.get('Avg. Trade [%]', 0):.2f}%
- **Expectancy**: {stats.get('Expectancy [%]', 0):.2f}%

**Trade Quality Metrics:**
- **Profit Factor**: {stats.get('Profit Factor', 0):.2f}
- **SQN (System Quality)**: {stats.get('SQN', 0):.2f}
- **Kelly Criterion**: {stats.get('Kelly Criterion', 0):.3f}

**Duration Analysis:**
- **Backtest Period**: {stats.get('Duration', 'N/A')}
- **Start Date**: {stats.get('Start', 'N/A')}
- **End Date**: {stats.get('End', 'N/A')}
- **Max Trade Duration**: {stats.get('Max. Trade Duration', 'N/A')}
- **Avg Trade Duration**: {stats.get('Avg. Trade Duration', 'N/A')}

"""

    def _generate_validated_mt4_ea(self, folder, ea_name, validation_results, backtest_results=None):
        """Generate MT4 EA from validated patterns using hard-coded conversion"""

        # If no validation_results, try to extract profitable patterns from backtest_results
        if not validation_results or not validation_results.get('success'):
            if backtest_results:
                print("   🔧 No validation results - extracting profitable patterns from backtest results")
                profitable_patterns = []
                for result in backtest_results:
                    if result.get('is_profitable', False):
                        # Extract pattern text from backtest result
                        pattern_text = result.get('pattern_text')
                        if pattern_text:
                            try:
                                # Parse the pattern text back into TradingPattern objects
                                from backtesting_rule_parser import BacktestingRuleParser
                                parser = BacktestingRuleParser()
                                parsed_patterns = parser.parse_llm_response(pattern_text)

                                # Add all parsed patterns from this profitable result
                                for pattern in parsed_patterns:
                                    profitable_patterns.append(pattern)
                                    print(f"      ✅ Added profitable Pattern '{pattern.pattern_name}': {result.get('return_pct', 0):.2f}% return")

                            except Exception as e:
                                print(f"      ⚠️ Failed to parse pattern text for Pattern {result.get('pattern_id', 'Unknown')}: {e}")
                                # Continue with other patterns instead of failing completely

                if profitable_patterns:
                    print(f"   ✅ Found {len(profitable_patterns)} profitable patterns from backtest results")
                    # Create synthetic validation_results
                    validation_results = {
                        'success': True,
                        'profitable_patterns': profitable_patterns
                    }
                else:
                    log_warning("   ⚠️ No profitable patterns found in backtest results - generating empty EA")
                    return self._save_empty_mt4_ea(folder, ea_name)
            else:
                log_warning("   ⚠️ No validation results or backtest results - generating empty EA")
                return self._save_empty_mt4_ea(folder, ea_name)

        profitable_patterns = validation_results.get('profitable_patterns', [])

        if not profitable_patterns:
            log_warning("   ⚠️ No profitable patterns - generating empty EA")
            return self._save_empty_mt4_ea(folder, ea_name)

        print(f"   🔧 Generating {len(profitable_patterns)} individual MT4 EAs (one per pattern)...")

        # Use hard-coded converter
        from mt4_hardcoded_converter import convert_profitable_patterns_to_mt4

        generated_ea_files = []

        # Generate individual EA for each profitable pattern
        # For multiple patterns, increment the EA number instead of using _Pattern_X
        for i, pattern in enumerate(profitable_patterns, 1):
            if len(profitable_patterns) == 1:
                # Single pattern: use base name as-is
                individual_ea_name = ea_name
            else:
                # Multiple patterns: increment the number in the base name
                # Extract base and number from ea_name (e.g., "GipsyDanger_DAX_001")
                parts = ea_name.rsplit('_', 1)
                if len(parts) == 2 and parts[1].isdigit():
                    base_name = parts[0]
                    base_number = int(parts[1])
                    individual_ea_name = f"{base_name}_{base_number + i - 1:03d}"
                else:
                    # Fallback if naming doesn't match expected pattern
                    individual_ea_name = f"{ea_name}_{i:03d}"

            individual_ea_file = os.path.join(folder, f"{individual_ea_name}.mq4")

            try:
                # Generate EA code for single pattern
                mt4_ea_code = convert_profitable_patterns_to_mt4([pattern], individual_ea_name)

                # Backup if file exists
                if os.path.exists(individual_ea_file):
                    backup_file = individual_ea_file + ".bak_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                    os.rename(individual_ea_file, backup_file)
                    print(f"      ⚠️ Existing EA backed up to {os.path.basename(backup_file)}")

                # Save individual EA file
                with open(individual_ea_file, 'w', encoding='utf-8') as f:
                    f.write(mt4_ea_code)

                generated_ea_files.append(individual_ea_file)
                print(f"      ✅ Individual EA: {os.path.basename(individual_ea_file)} (Pattern: {pattern.pattern_name})")

            except Exception as e:
                print(f"      ❌ Failed to generate EA for Pattern {i} ({pattern.pattern_name}): {e}")
                continue

        if generated_ea_files:
            print(f"   ✅ Generated {len(generated_ea_files)} individual MT4 Expert Advisors")
            return generated_ea_files  # Return list of files instead of single file
        else:
            print(f"   ❌ Failed to generate any MT4 EAs")
            return None

    def _save_empty_mt4_ea(self, folder, ea_name):
        """Save empty MT4 EA when no profitable patterns"""
        from mt4_hardcoded_converter import HardcodedMT4Converter

        converter = HardcodedMT4Converter()
        empty_ea_code = converter._generate_empty_ea(ea_name)

        ea_file = os.path.join(folder, f"{ea_name}.mq4")

        try:
            # Backup if file exists
            if os.path.exists(ea_file):
                backup_file = ea_file + ".bak_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                os.rename(ea_file, backup_file)
                print(f"   ⚠️ Existing MT4 EA backed up to {os.path.basename(backup_file)}")
            with open(ea_file, 'w', encoding='utf-8') as f:
                f.write(empty_ea_code)
            print(f"   ⚠️ Empty MT4 EA (no profitable patterns): {os.path.basename(ea_file)}")
            return [ea_file]  # Return as list for consistency
        except Exception as e:
            print(f"   ❌ Failed to write empty MT4 EA file: {e}")
            return None

    def _save_mt4_ea_file(self, folder, ea_name, mt4_ea_code):
        """Save MT4 Expert Advisor .mq4 file"""
        
        if not mt4_ea_code:
            log_warning("   ⚠️ No MT4 EA code provided")
            return None
        
        ea_file = os.path.join(folder, f"{ea_name}.mq4")
        
        try:
            # Backup if file exists
            if os.path.exists(ea_file):
                backup_file = ea_file + ".bak_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                os.rename(ea_file, backup_file)
                print(f"   ⚠️ Existing MT4 EA backed up to {os.path.basename(backup_file)}")
            with open(ea_file, 'w', encoding='utf-8') as f:
                f.write(mt4_ea_code)
            print(f"   ✅ MT4 Expert Advisor: {os.path.basename(ea_file)}")
            return ea_file
        except Exception as e:
            print(f"   ❌ Failed to write MT4 EA file: {e}")
            return None
    
    def _generate_html_charts(self, folder, symbol, ohlc_data, backtest_results):
        """Generate HTML interactive charts using backtesting.py"""

        if not backtest_results:
            log_warning("   ⚠️ No backtesting results - skipping HTML charts")
            return []

        html_files = []

        print("   🎨 Generating HTML charts using backtesting.py...")

        for i, result in enumerate(backtest_results, 1):
            stats = result.get('backtesting_py_stats')
            if stats is not None and not (hasattr(stats, 'empty') and stats.empty):
                try:
                    # CRITICAL FIX: Generate chart even with 0 trades
                    chart_file = os.path.join(folder, f"{symbol}_pattern_{i}_chart.html")

                    # Check if stats has plot method and try backtesting.py plotting first
                    if hasattr(stats, 'plot'):
                        try:
                            # Only try backtesting.py plot if there are actual trades
                            if hasattr(stats, '_trades') and not stats._trades.empty:
                                stats.plot(filename=chart_file, open_browser=False)
                                if os.path.exists(chart_file):
                                    html_files.append(chart_file)
                                    print(f"      ✅ Pattern {i} chart: {os.path.basename(chart_file)}")
                                    continue
                            else:
                                print(f"      ⚠️ Pattern {i} has no trades - using alternative chart generation (presentation only, not trading logic)")
                        except Exception as plot_error:
                            print(f"      ⚠️ backtesting.py plot failed for Pattern {i}: {plot_error}")

                    from chart_html_generator import HTMLChartGenerator
                    chart_gen = HTMLChartGenerator()

                    generated_chart = chart_gen.generate_backtest_html_chart(
                        stats=stats,
                        ohlc_data=ohlc_data,
                        symbol=symbol,
                        output_path=chart_file
                    )

                    if generated_chart and os.path.exists(chart_file):
                        html_files.append(chart_file)
                        print(f"      ✅ Pattern {i} chart: {os.path.basename(chart_file)}")
                    else:
                        print(f"      ❌ Failed to generate chart for Pattern {i}")

                except Exception as e:
                    print(f"      ❌ Chart generation failed for Pattern {i}: {e}")
            else:
                print(f"      ⚠️ No backtesting.py stats for Pattern {i} - skipping chart")

        return html_files
    
    def _generate_trade_csv_files(self, folder, symbol, backtest_results):
        """Generate CSV files with trade data from backtesting.py"""
        
        if not backtest_results:
            return []
        
        csv_files = []
        
        for i, result in enumerate(backtest_results, 1):
            stats = result.get('backtesting_py_stats')
            if stats is not None and not (hasattr(stats, 'empty') and stats.empty) and hasattr(stats, '_trades') and not stats._trades.empty:
                try:
                    # Export trades from backtesting.py to CSV
                    csv_file = os.path.join(folder, f"{symbol}_pattern_{i}_trades.csv")
                    try:
                        # Backup if file exists
                        if os.path.exists(csv_file):
                            backup_file = csv_file + ".bak_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                            os.rename(csv_file, backup_file)
                            print(f"   ⚠️ Existing trade CSV backed up to {os.path.basename(backup_file)}")
                        # Map backtesting.py columns to expected format
                        trades_df = stats._trades.copy()

                        # Create column mapping for backtesting.py format
                        column_mapping = {
                            'SL': 'StopLoss',
                            'TP': 'TakeProfit',
                            'PnL': 'Profit'
                        }

                        # Apply column mapping
                        trades_df = trades_df.rename(columns=column_mapping)

                        # Add Direction column based on Size (positive = long, negative = short)
                        if 'Direction' not in trades_df.columns and 'Size' in trades_df.columns:
                            trades_df['Direction'] = trades_df['Size'].apply(lambda x: 'Long' if x > 0 else 'Short')

                        # Ensure all expected columns exist
                        expected_cols = ['EntryTime', 'ExitTime', 'Direction', 'EntryPrice', 'ExitPrice', 'Profit', 'Size', 'StopLoss', 'TakeProfit']
                        for col in expected_cols:
                            if col not in trades_df.columns:
                                trades_df[col] = None  # Add missing columns with None values

                        # Reorder columns to match expected format
                        trades_df = trades_df[expected_cols + [col for col in trades_df.columns if col not in expected_cols]]

                        trades_df.to_csv(csv_file, index=False)
                        csv_files.append(csv_file)
                        print(f"   ✅ Trade data CSV: {os.path.basename(csv_file)}")
                    except Exception as e:
                        print(f"   ❌ Failed to write trade CSV for Pattern {i}: {e}")
                    
                except Exception as e:
                    print(f"   ❌ Failed to generate CSV for Pattern {i}: {e}")
        
        return csv_files
